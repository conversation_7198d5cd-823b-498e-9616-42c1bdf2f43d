<template>
  <div class="goods-item">
    <div>
      <img
        :src="
          itemData.productMinImg
            ? imgUrlPrefixAdd + itemData.productMinImg
            : require('@/assets/images/img/queshen5.png')
        "
        alt=""
        @click="
          openWindowTab({
            path: '/mFront/productDetail',
            query: { productId: itemData.productId },
          })
        "
      />
      <div class="name textOverflowName">{{ itemData.productName }}</div>
      <div
        class="type dfc textOverflowType"
      >
        {{ itemData.skuName }}
      </div>
      <div
        style="
          display: flex;
          align-items: center;
          justify-content: space-between;
          width: 100%;
        "
      >
        <div class="price" style="margin-left: 10px">
          ￥<span>{{ itemData.sellPrice }}</span>
        </div>
        <div style="width: 100px">
          <QuantitySelector
            v-model="itemData.goodsNum"
            @changeSelector="handleChange"
            :min="1"
            :max="100000"
          />
        </div>
        <img
          style="width: 20px; height: 20px; margin-right: 10px"
          src="../assets/images/shopping_cart.png"
          @click="handleView"
          alt=""
        />
      </div>
      <el-dialog :visible.sync="shoppingShowDialog" width="30%" class="dialog-shopping" @close="resetForm">
          <template #title>
              <div class="tabs-title">
                  <span>商品信息选择</span>
              </div>
           </template>
        <el-form
          :model="form"
          :rules="rules"
          ref="ruleForm"
          style="margin-right: 30px; margin-top: 40px"
        >
          <el-form-item label="账期：" label-width="100px" prop="accountPeriod">
            <el-select
              v-model="form.accountPeriod"
              placeholder="请选择账期"
              style="width: 400px"
              @change="changeAccountPeriod"
            >
              <el-option
                v-for="item in getPaymentPeriodOptions (product)"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="区域：" label-width="100px" prop="region">
            <el-select
              v-model="form.region"
              placeholder="请选择区域"
              style="width: 400px"
              @change="changeRegion"
            >
              <el-option
                v-for="item in getDeliveryAreaOptions()"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button @click="shoppingShowDialog = false">取 消</el-button>
          <el-button type="primary" @click="handleAddToCart" :loading="addToCartLoading">确 定</el-button>
        </span>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import {
    addCart,
    getLogInMaterialInfo,
    getMaterialInfo,
} from '@/api/frontStage/productDetail'
import QuantitySelector from '@/components/quantity-selector'
export default {
    name: 'goodItem',
    components: {
        QuantitySelector,
    },
    props: {
        itemData: Object,
    },
    data () {
        return {
            goodsNum: 1,
            productInfo: {},
            shoppingShowDialog: false,
            addToCartLoading: false,
            showClose: true,
            form: {
                accountPeriod: '',
                region: '',
                zoneId: '',
                zoneAddr: '',
                regionPriceId: '', // 价格区域id
            },
            deliveryAreaOptions: [
                { label: '四川省成都市', value: 'chengdu' },
                { label: '四川省自贡市', value: 'zigong' },
                { label: '四川省攀枝花市', value: 'panzhihua' },
                { label: '四川省泸州市', value: 'luzhou' },
                { label: '四川省德阳市', value: 'deyang' },
                { label: '四川省绵阳市', value: 'mianyang' },
                { label: '四川省广元市', value: 'guangyuan' },
                { label: '四川省遂宁市', value: 'suining' },
                { label: '四川省内江市', value: 'neijiang' },
                { label: '四川省乐山市', value: 'leshan' },
                { label: '四川省南充市', value: 'nanchong' },
                { label: '四川省眉山市', value: 'meishan' },
                { label: '四川省宜宾市', value: 'yibin' },
                { label: '四川省广安市', value: 'guangan' },
                { label: '四川省达州市', value: 'dazhou' },
                { label: '四川省雅安市', value: 'yaan' },
                { label: '四川省巴中市', value: 'bazhong' },
                { label: '四川省资阳市', value: 'ziyang' },
                { label: '四川省阿坝藏族羌族自治州', value: 'aba' },
                { label: '四川省甘孜藏族自治州', value: 'ganzi' },
                { label: '四川省凉山彝族自治州', value: 'liangshan' },
            ],
            accountPeriodOption: [
                {
                    value: '1',
                    label: '1个月账期',
                },
                {
                    value: '2',
                    label: '2个月账期',
                },
                {
                    value: '3',
                    label: '3个月账期',
                },
                {
                    value: '4',
                    label: '4个月账期',
                },
                {
                    value: '5',
                    label: '5个月账期',
                },
                {
                    value: '6',
                    label: '6个月账期',
                },
            ],
            rules: {
                accountPeriod: [
                    { required: true, message: '请选择账期', trigger: 'blur' },
                ],
                region: [{ required: true, message: '请选择区域', trigger: 'blur' }],
            },
        }
    },
    mounted () {
    },

    methods: {
        resetForm () {
            if (this.$refs.ruleForm) {
                this.$refs.ruleForm.clearValidate()
            }
        },
        handleAddToCart () {
            this.$refs.ruleForm.validate(valid => {
                if (valid) {
                    this.addToCartLoading = true
                    addCart({
                        cartNum: this.goodsNum,
                        productId: this.productInfo.productId,
                        zoneId: this.form.zoneId,
                        zoneAddr: this.form.zoneAddr,
                        paymentPeriod: this.form.accountPeriod,
                        regionPriceId: this.form.regionPriceId, // 传递价格区域id
                    })
                        .then(res => {
                            if (res.code == 200) {
                                this.$message({
                                    message: '加入购物车成功！',
                                    type: 'success',
                                })
                                this.$bus.$emit('refreshCart')
                                this.shoppingShowDialog = false
                            }
                        })
                        .catch(() => {
                        })
                        .finally(() => {
                            this.addToCartLoading = false
                        })
                } else {
                    return false
                }
            })

        },

        // 获取商品的账期选项
        getPaymentPeriodOptions () {
            if (!this.productInfo.regionPriceList || !Array.isArray(this.productInfo.regionPriceList)) {
                return []
            }

            // 从regionPriceList中提取所有的accountPeriod
            const accountPeriods = new Set()
            this.productInfo.regionPriceList.forEach(regionPrice => {
                if (regionPrice.accountPeriod !== undefined && regionPrice.accountPeriod !== null) {
                    accountPeriods.add(regionPrice.accountPeriod)
                }
            })

            // 转换为选项格式并排序
            const options = Array.from(accountPeriods).map(period => ({
                label: `${period}个月账期`,
                value: period
            })).sort((a, b) => a.value - b.value)

            return options
        },

        // 获取商品当前账期对应的配送区域选项
        getDeliveryAreaOptions () {
            if (!this.productInfo.regionPriceList || !Array.isArray(this.productInfo.regionPriceList)) {
                return []
            }

            // 过滤当前账期对应的regionPrice
            const filteredRegionPrices = this.productInfo.regionPriceList.filter(regionPrice => {
                return regionPrice.accountPeriod === this.productInfo.paymentPeriod
            })

            // 提取配送区域选项
            const areaMap = new Map()

            filteredRegionPrices.forEach(regionPrice => {
                if (regionPrice.area && regionPrice.areaCode) {
                    // 拆分area字符串（如"北京市,天津市"）
                    const areaNames = regionPrice.area.split(',').map(name => name.trim())
                    // 解析areaCode数组（如["110000","120000"]）
                    let areaCodes = []
                    try {
                        areaCodes = Array.isArray(regionPrice.areaCode)
                            ? regionPrice.areaCode
                            : JSON.parse(regionPrice.areaCode)
                    } catch (e) {
                        areaCodes = [regionPrice.areaCode]
                    }

                    // 将area和areaCode一一对应展示
                    areaNames.forEach((areaName, index) => {
                        const areaCode = areaCodes[index] || areaCodes[0] // 如果数量不匹配，使用第一个code
                        if (areaName && areaCode && !areaMap.has(areaCode)) {
                            areaMap.set(areaCode, {
                                label: areaName, // 单个区域名称
                                value: areaCode // 单个区域代码
                            })
                        }
                    })
                }
            })

            // 转换为数组并排序
            return Array.from(areaMap.values()).sort((a, b) => a.label.localeCompare(b.label))
        },
        // 根据zoneId获取配送区域的中文名称
        getDeliveryAreaName (product, zoneId) {
            if (!zoneId) return ''

            const deliveryOptions = this.getDeliveryAreaOptions(product)
            const option = deliveryOptions.find(opt => opt.value === zoneId)
            return option ? option.label : zoneId
        },

        // 根据选择的区域获取对应的regionPriceId
        getRegionPriceId (areaCode) {
            if (!this.productInfo.regionPriceList || !Array.isArray(this.productInfo.regionPriceList) || !areaCode) {
                this.form.regionPriceId = ''
                return
            }

            // 过滤当前账期对应的regionPrice
            const filteredRegionPrices = this.productInfo.regionPriceList.filter(regionPrice => {
                return regionPrice.accountPeriod === this.form.accountPeriod
            })

            // 查找匹配的区域价格信息
            for (let regionPrice of filteredRegionPrices) {
                if (regionPrice.areaCode) {
                    let areaCodes = []
                    try {
                        areaCodes = Array.isArray(regionPrice.areaCode)
                            ? regionPrice.areaCode
                            : JSON.parse(regionPrice.areaCode)
                    } catch (e) {
                        areaCodes = [regionPrice.areaCode]
                    }

                    // 检查是否包含当前选择的区域代码
                    if (areaCodes.includes(areaCode)) {
                        this.form.regionPriceId = regionPrice.regionPriceId
                        return
                    }
                }
            }

            // 如果没有找到匹配的，清空regionPriceId
            this.form.regionPriceId = ''
        },
        // 获取商品详情
        getProductInfoM () {
            let params = {
                productId: this.itemData.productId,
            }
            if (!localStorage.getItem('token')) {
                getMaterialInfo(params)
                    .then(res => {
                        this.productInfo = res
                        if (this.productInfo.shopState === 0) {
                            return this.$message.error('该店铺已被冻结，无法购买商品')
                        } else {
                            this.form.accountPeriod = null
                            this.form.region = null
                            this.shoppingShowDialog = true
                        }
                    })
                    .catch(() => {
                        this.$message({
                            message: '商品不存在或商品已下架',
                            type: 'warning',
                        })
                    })
            } else {
                getLogInMaterialInfo(params)
                    .then(res => {
                        this.productInfo = res
                        if (this.productInfo.shopState === 0) {
                            return this.$message.error('该店铺已被冻结，无法购买商品')
                        } else {
                            this.form.accountPeriod = null
                            this.form.region = null
                            this.shoppingShowDialog = true
                        }
                    })
                    .catch(() => {
                        this.$message({
                            message: '商品不存在或商品已下架',
                            type: 'warning',
                        })
                    })
            }
        },
        handleChange (goodsNum) {
            this.goodsNum = goodsNum
        },

        changeRegion (row) {
            // this.form.zoneAddr = row.zoneAddr
            // this.form.zoneId = row.zoneId
            // console.log('changeRegion', row)
            this.form.zoneId = row

            // 根据选择的区域获取对应的regionPriceId
            this.getRegionPriceId(row)
        },

        // 账期改变时的处理
        changeAccountPeriod () {
            // 账期改变时，如果已选择区域，需要重新获取regionPriceId
            if (this.form.zoneId) {
                this.getRegionPriceId(this.form.zoneId)
            }
        },
        handleView () {
            this.getProductInfoM()
            this.$nextTick(() => {
                this.resetForm()
            })
        },
    },
}
</script>

<style scoped lang="scss">
@import '../assets/css/floor.scss';

.dialog-shopping {
  /deep/ .el-dialog__header {
    padding: 0;
  }
  .tabs-title {
    margin-left: 20px;
    border-left: #2e61d7 6px solid;
    padding-left: 6px;
  }
  /deep/ .el-dialog__footer {
    padding: 20px 20px 10px 0;
  }
}
.textOverflowName {
  max-width: 250px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.textOverflowType {
  overflow: hidden;height: 28px;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  // text-align: center;
  word-break: break-all;
}
/*.tabs-title::before {
  content: '';
  height: 15px;
  width: 6px;
  border-radius: 2px;
  background-color: #2e61d7;
  // display: block;
  position: absolute;
  left: -2px;
  margin-top: 1px;
  margin-left: 10px;
  margin-right: 12px;
}*/

:deep(.el-button) {
  padding: 0px 20px !important;
}
:deep(.el-button--text) {
  color: #3b3c3d !important;
}
// :deep( .el-select){
//   display: block  !important;
//   position: relative;
// }
</style>
