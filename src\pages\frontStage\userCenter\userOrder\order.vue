<template>
  <main class="userCenter" v-loading="showLoading">
    <div class="title">我的订单</div>
    <div class="content p20">
      <div class="tabs mb20 dfb">
        <div class="tab df">
            <div :class="activeTab == -1 ? 'active' : ''" @click="checkActiveTab(-1)">全部订单</div>
            <div :class="activeTab == 6 ? 'active' : ''" @click="checkActiveTab(6)">待发货</div>
            <div :class="activeTab == 8 ? 'active' : ''" @click="checkActiveTab(8)">已发货</div>
            <div :class="activeTab == 9 ? 'active' : ''" @click="checkActiveTab(9)">待收货</div>
            <div :class="activeTab == 10 ? 'active' : ''" @click="checkActiveTab(10)">已完成</div>
          <!--          <div :class="activeTab == 1 ? 'active' : ''" @click="activeTab = 1">待付款</div>-->
          <!--          <div :class="activeTab == 2 ? 'active' : ''" @click="activeTab = 2">待收货</div>-->
          <!--                    <div :class="activeTab == 3 ? 'active' : ''" @click="activeTab = 3">待评价（0）</div>-->
        </div>
        <div class="search df">
            <div v-if="showDevFunc" class="mr30">
                <!--                    数据权限-->
                <el-select :value="dataSource" @change="dataSourceChange">
                    <el-option label="本级及下级" :value="0"/>
                    <el-option label="本机构" :value="1"/>
                    <el-option v-if="userPermission.hasSubOrg()" label="下级机构" :value="2"/>
                    <el-option
                        v-for="item in userPermission.subOrg"
                        :label="item.orgName" :value="item.orgId" :key="item.orgId"
                    />
                </el-select>
            </div>
          <div class="box dfa">
            <img src="@/assets/images/ico_search.png" alt="">
            <input v-model="keyword" type="text" placeholder="订单号/计划编号/供应商">
          </div>
          <button @click="onSearch">搜索</button>
        </div>
      </div>
      <div class="titleBar mb20 dfa">
        <el-select v-model="selectedVal" value-key="" @change="handleFilter">
          <el-option v-for="item in selectOptions"
                     :key="item.value"
                     :label="item.label"
                     :value="item.value">
          </el-option>
        </el-select>
        <span>订单详情</span>
        <span>计划编号</span>
        <span>收货人</span>
        <span>金额</span>
        <span>状态</span>
        <span>操作</span>
          <span v-if="showDevFunc">机构信息</span>
      </div>
      <div class="orderList">
        <div class="item mb10" v-for="item in list" :key="item.id">
          <div class="itemHead dfa">
            <div class="left">
              <!-- <span>{{ item.createTime }}</span><span>订单号：{{ item.orderNum }}</span> -->
               <span>{{item.enterpriseName}}</span>
            </div>
            <!--                        <div class="right dfa" v-if="item.status == 1">-->
            <!--                            <img src="@/assets/images/userCenter/ico_time.png" alt="">-->
            <!--                            <span>剩余{{ item.countdown }}</span>-->
            <!--                        </div>-->
            <!--            <div class="right">-->
            <!--              <el-button type="danger">退货</el-button>-->
            <!--            </div>-->
          </div>
          <div class="itemContent df">
            <!-- <img @click="$router.push({ path: '/mFront/productDetail', query: { productId: item.productId } })" :src="item.pictureUrl ? imgUrlPrefixAdd + item.pictureUrl : require('@/assets/images/img/queshen5.png')" alt="">
            <p @click="$router.push({ path: '/mFront/productDetail', query: { productId: item.productId } })">{{ item.title }}</p> -->
            <div class="orderCode"  style="color: #226fc7;" @click="handleViewDetail(item.orderNum)">订单号：{{ item.orderNum }}</div>
            <!-- <div class="pointer"  style="color: #226fc7;" @click="handleViewDetail(item.orderNum)">{{ item.billNo }}</div> -->
            <div class="planCode"><span>{{item.billNo}}</span></div>
            <div class="cs"><span class="pointer" v-if="item.status == 4" @click="applyCustomerService(item.orderId)">申请售后</span></div>
            <div class="receiver">{{ item.receiver }}</div>
            <div class="price">￥{{ item.price }}</div>
            <div class="status">
              <div class="mb10">
                  <span v-if="item.status == 0">草稿</span>
                  <span v-if="item.status == 1">已提交</span>
                  <span v-if="item.status == 2">待确认</span>
                  <span v-if="item.status == 3">已确认</span>
                  <span v-if="item.status == 4">待签订合</span>
                  <span v-if="item.status == 5">已签合同</span>
                  <span v-if="item.status == 6">待发货</span>
                  <span v-if="item.status == 7">已关闭</span>
                  <span v-if="item.status == 8">发货中</span>
                  <span v-if="item.status == 9">待收货</span>
                  <span v-if="item.status == 10">已完成</span>
              </div>
              <div class="pointer"  style="color: #226fc7;" @click="handleViewDetail(item.orderNum)">订单详情</div>
            </div>
            <div class="status">
              <!--              <div style="color: #d43030;" v-if="item.status == 2" @click="handleOrderSettlement(item.orderId)">付款-->
              <!--              </div>-->
              <!--                                <span style="color: #226fc7;" v-if="item.status == 7" @click="handleOrderReceive(item.orderId)">确认收货/</span>-->
              <!--<div class="pointer"   style="color: #226fc7;" v-if="item.status == 10" >-->
                <!--<div v-if="item.invoiceState==1">已申请发票</div>-->
                <!--<div  v-else @click="invoiceApply(item)">-->
                <!--  申请发票-->
                <!--</div>-->
              <!--</div>-->
              <div class="pointer"   style="color: #226fc7; margin-top: 10px" v-if="item.quantity>item.shipCounts+item.returnCounts-item.pcwpReturn  && userPermission.isSameOrgByEnterpriseId(item.enterpriseId)"  >
                <span v-if="(item.returnState==null||item.returnState==0)&&item.productType==0 " @click="orderReturn(item)" >申请退货</span>
<!--                <span v-show="item.status>6&&item.productType==10&&(item.returnState==null||item.returnState==0)" @click="orderReturn(item)" >申请退货</span>-->
                <span v-show="item.returnState==1"  >审核中</span>
                <span style="color: #e39696;" v-show="item.returnState==2"   >退货中</span>
                <span style="color: limegreen;" v-show="item.returnState==3"  >退货成功</span>
                <span style="color: red;" v-show="item.returnState==4"  >退货失败</span>
              </div>
                <div  class="pointer" style="color: #226fc7; margin-top: 8px; "
                      v-if="item.returnState == 3 && item.quantity>(item.returnCounts+item.shipCounts-item.pcwpReturn)  && userPermission.isSameOrgByEnterpriseId(item.enterpriseId)" @click="orderReturn(item)">再次退货</div>
                <div  class="pointer" style="color: #226fc7;  margin-top: 8px; "
                      v-if="item.returnState == 4 && item.quantity>(item.returnCounts+item.shipCounts-item.pcwpReturn)  && userPermission.isSameOrgByEnterpriseId(item.enterpriseId)" @click="orderReturn(item)">重新退货</div>
<!--                <div class="pointer" style="color: #226fc7;  margin-top: 8px; margin-left: 5px"  v-if="item.status >= 8 && item.status < 10  && userPermission.isSameOrgByEnterpriseId(item.enterpriseId)"   @click="orderCloseClick(item)">完结订单</div>-->
<!--                      v-if="item.returnState == 4 && item.quantity>(item.returnCounts+item.shipCounts)" @click="orderReturn(item)">重新退货</div>-->
               <div class="pointer" style="color: #226fc7;  margin-top: 8px; margin-left: 5px"
                     v-if="item.status == 10" @click="deleteOrderInfo(item.orderId)">删除订单</div>
               <div  class="pointer" style="color: #226fc7; margin-top: 8px; margin-left: 5px"
                                v-if="item.status == 10" @click="repurchase(item)">一键复购</div>
<!--                <div class="pointer" style="color: #226fc7;  margin-top: 8px; margin-left: 5px"  v-if="item.status >= 8 && item.status < 10"   @click="orderCloseClick(item)">完结订单</div>-->
              <div>
                <!--                                <span v-if="item.status == 0" @click="cancelOrder(item.orderId)">取消订单</span>-->
                <!--                                <span v-else @click="handlePurchase(item.orderId)">再次购买</span>-->
              </div>
            </div>
              <div class="en-name" v-if="showDevFunc">{{ item.enterpriseName }}</div>
          </div>
        </div>
      </div>
      <pagination :currentPage.sync="pagination.currPage" :destination="pagination.destination" :pageSize="pagination.pageSize"
                  :total="pagination.totalNum" :totalPage="pagination.totalPage" @currentChange="currentChange" @sizeChange="sizeChange">
      </pagination>
    </div>
  </main>
</template>
<script>
import pagination from '@/pages/frontStage/components/pagination'
import { deleteLXOrderByOrderId, getUserOrderPageList } from '@/api/frontStage/order'
import { orderCloseClick } from '@/api/platform/order/orders'
import { UserPermission } from '@/utils/permissions'
import { addCart, } from '@/api/frontStage/productDetail'
export default {
    components: { pagination },
    data () {
        return {
            showLoading: false,
            activeTab: -1,
            selectedVal: 0,
            keyword: null,
            state: null,
            selectOptions: [
                { label: '近一个月订单', value: 0 },
                { label: '近三个月订单', value: 1 },
                { label: '近半年订单', value: 2 },
                { label: '全部订单', value: 3 },
            ],
            list: [
            ],
            pagination: {
                currPage: 1, //当前页
                destination: null,
                pageSize: 3, // 显示数量
                totalNum: null,
                totalPage: null,
            },
            destination: 2,
            userPermission: new UserPermission('物资下单权限'),
            dataSource: 0,
        }
    },
    watch: {
        activeTab (num) {
            if(num == -1) {
                this.state = null
            }
            if(num == 0) {
                this.state = 0
            }
            if(num == 6) {
                this.state = 6
            }
            if(num == 7) {
                this.state = 7
            }
            if(num == 8) {
                this.state = 8
            }
            if(num == 9) {
                this.state = 9
            }
            if(num == 10) {
                this.state = 10
            }
            this.getUserOrderPageListM()
        },
    },
    created () {
        this.getUserOrderPageListM()
    },
    mounted () {},
    methods: {
        //数据权限
        dataSourceChange (state) {
            this.dataSource = state
            if (state === 0) {
                this.userPermission.getAllOrgData()
            } else if (state === 1) {
                this.userPermission.getHostOrgData()
            } else if (state === 2) {
                this.userPermission.getSubOrgData()
            } else if (state.length > 1) {
                this.userPermission.getSubOrgData(state)
            }
            this.getUserOrderPageListM()
        },
        // 删除订单
        deleteOrderInfo (orderId) {
            this.clientPop('info', '您确认要删除订单吗？', async () => {
                this.showLoading = true
                deleteLXOrderByOrderId({ orderId: orderId }).then(res => {
                    if (res.code === 200) {
                        this.clientPop('suc', '操作成功！', () => {
                            this.getUserOrderPageListM()
                        })
                    }
                }).finally(() => {
                    this.showLoading = false
                })
            })
        },
        orderCloseClick (row) {
            this.clientPop('info', '您确定要完结订单吗？未执行数量将还回计划，不能再发货操作！', async () => {
                this.showLoading = true
                orderCloseClick({ orderId: row.orderId }).then(res => {
                    if (res.code != null && res.code == 200) {
                        this.$message.success('操作成功')
                        this.getUserOrderPageListM()
                    }
                }).finally(() => {
                    this.showLoading = false
                })
            })
        },
        // 标签点击
        checkActiveTab (num) {
            this.activeTab = num
        },
        invoiceApply (item) {
            this.$router.push(
                { path: '/user/myInvoice/invoice/company',
                    name: 'companyApply',
                    params: {
                        row: item
                    }
                })
        },
        repurchase (row) {
            console.log(row)
            // if (this.productInfo.shopState == 0) {
            //     return this.$message.error('该店铺已被冻结，无法购买商品')
            // }
            addCart({ cartNum: row.quantity, productId: row.productId }).then(res => {
                if (res.code == 200) {
                    this.$message({
                        message: '已加入购物车！',
                        type: 'success'
                    })
                    this.$bus.$emit('refreshCart')
                }
            }).catch(() => {
            })
        },
        orderReturn (item) {
            this.$router.push(
                // { path: '/user/refund/unshipped',
                { path: '/user/refund/invoice',
                    name: 'refundApply',
                    params: {
                        row: item
                    }
                })
        },
        getLastMonth () {
            let now = new Date()
            let year = now.getFullYear()
            let month = now.getMonth() + 1//0-11表示1-12月
            let day = now.getDate()
            let dateObj = {}
            dateObj.now = year + '-' + month + '-' + day
            let nowMonthDay = new Date(year, month, 0).getDate()    //当前月的总天数
            if(month - 1 <= 0) { //如果是1月，年数往前推一年<br>
                dateObj.last = (year - 1) + '-' + 12 + '-' + day
            }else{
                let lastMonthDay = new Date(year, (parseInt(month) - 1), 0).getDate()
                if(lastMonthDay < day) {    // 1个月前所在月的总天数小于现在的天日期
                    if(day < nowMonthDay) {        //当前天日期小于当前月总天数
                        dateObj.last = year + '-' + (month - 1) + '-' + (lastMonthDay - (nowMonthDay - day))
                    }else{
                        dateObj.last = year + '-' + (month - 1) + '-' + lastMonthDay
                    }
                }else{
                    dateObj.last = year + '-' + (month - 1) + '-' + day
                }
            }
            return dateObj
        },
        getLast3Month () {
            let now = new Date()
            let year = now.getFullYear()
            let month = now.getMonth() + 1//0-11表示1-12月
            let day = now.getDate()
            let dateObj = {}
            dateObj.now = year + '-' + month + '-' + day
            let nowMonthDay = new Date(year, month, 0).getDate()    //当前月的总天数
            if(month - 3 <= 0) { // 如果是1、2、3月，年数往前推一年
                var last3MonthDay1 = new Date((year - 1), (12 - (3 - parseInt(month))), 0).getDate()    // 3个月前所在月的总天数
                if(last3MonthDay1 < day) {    // 3个月前所在月的总天数小于现在的天日期
                    dateObj.last = (year - 1) + '-' + (12 - (3 - month)) + '-' + last3MonthDay1
                }else{
                    dateObj.last = (year - 1) + '-' + (12 - (3 - month)) + '-' + day
                }
            }else{
                let last3MonthDay2 = new Date(year, (parseInt(month) - 3), 0).getDate()    //3个月前所在月的总天数
                if(last3MonthDay2 < day) {    //3个月前所在月的总天数小于现在的天日期
                    if(day < nowMonthDay) {        //当前天日期小于当前月总天数,2月份比较特殊的月份
                        dateObj.last = year + '-' + (month - 3) + '-' + (last3MonthDay2 - (nowMonthDay - day))
                    }else{
                        dateObj.last = year + '-' + (month - 3) + '-' + last3MonthDay2
                    }
                }else{
                    dateObj.last = year + '-' + (month - 3) + '-' + day
                }
            }
            return dateObj
        },
        getLast6Month () {
            let now = new Date()
            let year = now.getFullYear()
            let month = now.getMonth() + 1//0-11表示1-12月
            let day = now.getDate()
            let dateObj = {}
            dateObj.now = year + '-' + month + '-' + day
            let nowMonthDay = new Date(year, month, 0).getDate()    //当前月的总天数
            if(month - 6 <= 0) { // 年数往前推一年
                let last6MonthDay1 = new Date((year - 1), (12 - (6 - parseInt(month))), 0).getDate()    // 6个月前所在月的总天数
                if(last6MonthDay1 < day) {    // 6个月前所在月的总天数小于现在的天日期
                    dateObj.last = (year - 1) + '-' + (12 - (6 - month)) + '-' + last6MonthDay1
                }else{
                    dateObj.last = (year - 1) + '-' + (12 - (6 - month)) + '-' + day
                }
            }else{
                let last6MonthDay2 = new Date(year, (parseInt(month) - 6), 0).getDate()    //6个月前所在月的总天数
                if(last6MonthDay2 < day) {    //6个月前所在月的总天数小于现在的天日期
                    if(day < nowMonthDay) {        //当前天日期小于当前月总天数,2月份比较特殊的月份
                        dateObj.last = year + '-' + (month - 6) + '-' + (last6MonthDay2 - (nowMonthDay - day))
                    }else{
                        dateObj.last = year + '-' + (month - 6) + '-' + last6MonthDay2
                    }
                }else{
                    dateObj.last = year + '-' + (month - 6) + '-' + day
                }
            }
            return dateObj
        },
        getUserOrderPageListM () {
            let params = {
                page: this.pagination.currPage,
                limit: this.pagination.pageSize,
                productType: 0,
                // 数据权限
                dataSelect: this.userPermission.orgDisplayState, // （1本机及子级2只看本级3看指定子级）
                dataScope: this.userPermission.currentSubOrgId
            }
            if(this.state >= 0) {
                params.state = this.state
            }
            if(this.selectedVal === 0) {
                let dateObj = this.getLastMonth()
                params.startDate = dateObj.last + ' 00:00:00'
                params.endDate = dateObj.now + ' 23:59:59'
            }
            if(this.selectedVal === 1) {
                let dateObj = this.getLast3Month()
                params.startDate = dateObj.last + ' 00:00:00'
                params.endDate = dateObj.now + ' 23:59:59'
            }
            if(this.selectedVal === 2) {
                let dateObj = this.getLast6Month()
                params.startDate = dateObj.last + ' 00:00:00'
                params.endDate = dateObj.now + ' 23:59:59'
            }
            if(this.keyword != null) {
                params.keywords = this.keyword
            }
            this.showLoading = true
            getUserOrderPageList(params).then(res => {
                this.list = []
                res.list.forEach(t => {
                    this.list.push({
                        productId: t.productId,
                        shipCounts: t.shipCounts,
                        shopId: t.shopId,
                        orderId: t.orderId,
                        orderNum: t.orderSn,
                        billNo: t.billNo,
                        pictureUrl: t.productImg,
                        title: t.untitled,
                        returnCounts: t.returnCounts,
                        pcwpReturn: t.pcwpReturn,
                        quantity: t.buyCounts,
                        receiver: t.receiverName,
                        price: t.actualAmount,
                        status: t.state,
                        productType: t.productType,
                        createTime: t.gmtCreate,
                        invoiceState: t.invoiceState,
                        returnState: t.returnState,
                        brand: t.brand,
                        enterpriseId: t.enterpriseId,
                        enterpriseName: t.enterpriseName,
                        confirmCounts: t.confirmCounts,
                        maxReturnQty: t.maxReturnQty,
                        orderItemId: t.orderItemId
                    })
                })
                this.pagination.currPage = res.currPage
                this.pagination.pageSize = res.pageSize
                this.pagination.totalNum =  res.totalCount
                this.pagination.totalPage =  res.totalPage
                this.showLoading = false
            }).catch(() => {
                this.showLoading = false
            })
        },
        currentChange (index) {
            this.pagination.currPage = index
            this.getUserOrderPageListM()
        },
        sizeChange (size) {
            this.pagination.pageSize = size
            this.getUserOrderPageListM()
        },
        // 下拉筛选
        handleFilter () {
            this.getUserOrderPageListM()
        },
        // 搜索
        onSearch () {
            this.getUserOrderPageListM()
        },
        // 申请售后
        applyCustomerService (id) {
            console.log(id)
        },
        // 跳转订单详情页面
        handleViewDetail (id) {
            this.$router.push({ path: '/user/orderPlanDetail', query: { orderSn: id } })
        },
        // 评价订单
        handleOrderReview (id) {
            console.log(id)
        },
        // 订单付款
        handleOrderSettlement (id) {
            console.log(id)
        },
        // 取消订单
        cancelOrder (id) {
            console.log(id)
        },
        // 再次购买
        handlePurchase (id) {
            console.log(id)
        },
        // 确认收货
        handleOrderReceive (id) {
            console.log(id)
        },
    },
}
</script>
<style scoped lang="scss">
main {border: 1px solid rgba(230, 230, 230, 1);}
.content {
  height: 824px;
  .tabs {
    // margin-top: 20px;
    .tab {
      font-size: 16px;
      color: rgba(102, 102, 102, 1);
      div {
        margin-right: 50px;
        cursor: pointer;
      }
      .active {
        color: rgba(0, 0, 0, 1);
        &::after {
          content: '';
          display: block;
          width: 100%;
          height: 2px;
          margin-top: 4px;
          background-color: rgba(34, 111, 199, 1);
        }
      }
    }
    .search {
      .box {
        width: 268px;
        height: 26px;
        border: 1px solid rgba(229, 229, 229, 1);
        border-right: 0;
        img {
          width: 16px;
          height: 16px;
          margin: 0 4px 0 10px;
        }
        input {width: 230px;}
      }
      button {
        width: 52px;
        height: 26px;
        font-size: 14px;
        color: #fff;
        background-color: rgba(212, 48, 48, 1);
      }
    }
  }
  .titleBar {
    height: 50px;
    padding: 0 20px;
    border: 1px solid rgba(230, 230, 230, 1);
    background-color: rgba(250, 250, 250, 1);
    /deep/ .el-select {
      //margin-right: 54px;
      &, .el-input {width: 104px;}
      .el-input__inner {
        padding-left: 0;
        padding-right: 20px;
        border: 0;
        color: rgba(0, 0, 0, 1);
      }
      .el-select__caret {
        background-image: url(../../../../assets/images/userCenter/arrow_up.png);
        background-position: 50% 50%;
        background-repeat: no-repeat;
        &::before{content: '';}
      }
    }
    span:nth-of-type(1) {margin-right: 160px !important;}
    span:not(:last-of-type) {margin-right: 100px;}
  }
  .orderList {
    height: 576px;
    .item {
      border: 1px solid rgba(230, 230, 230, 1);
      .itemHead {
        height: 40px;
        padding: 0 20px;
        border-bottom: 1px solid rgba(230, 230, 230, 1);
        background-color: rgba(250, 250, 250, 1);
        position: relative;
        .left {
          color: rgba(51, 51, 51, 1);
          span {margin-right: 24px;}
        }
        .right {
          width: 95px;
          font-size: 12px;
          color: rgba(153, 153, 153, 1);
          position: absolute;
          right: 20px;
          img {
            width: 16px;
            height: 16px;
            margin-right: 4px;
          }
        }
      }
      .itemContent {
        height: 140px;
        padding: 30px 20px;
        align-items: center;
        img {
          width: 80px;
          height: 80px;
          margin-right: 16px;
        }
        p {
          width: 236px;
          margin-right: 20px;
          color: rgba(51, 51, 51, 1);
        }
        .orderCode{
          width: 260px;
          cursor: pointer;
          align-items: center;
        }
        .quantity {
          width: 100px;
        }
        .planCode {
          width: 100px;
          margin-left: 40px;
        }
        .cs {
          width: 56px;
          //margin-right: 3px;
        }
        .receiver {
          width: 120px;
          text-align: center;
        }
        .price{
          width: 140px;
          text-align: center;
        }
          .en-name{
              width: 100px;
          }
         .status {
          width: 150px;
          text-align: center;
        }
        .actions span {cursor: pointer;}
      }
    }
  }
}
/deep/ .el-input--suffix .el-input__inner{
    height: 26px;
}
</style>